using Microsoft.Extensions.Logging;
using SubtitlesParser.Classes;
using VideoContentAnalyzer.Core.Interfaces;
using VideoContentAnalyzer.Core.Models;

namespace VideoContentAnalyzer.Infrastructure.Subtitle;

public class SubtitleService : ISubtitleService
{
    private readonly IWhisperService _whisperService;
    private readonly ILogger<SubtitleService> _logger;

    public SubtitleService(IWhisperService whisperService, ILogger<SubtitleService> logger)
    {
        _whisperService = whisperService;
        _logger = logger;
    }

    public async Task<List<SubtitleSegment>> ParseSubtitleFileAsync(string subtitlePath, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Parsing subtitle file: {SubtitlePath}", subtitlePath);
        
        if (!File.Exists(subtitlePath))
        {
            throw new FileNotFoundException($"Subtitle file not found: {subtitlePath}");
        }

        try
        {
            var format = await DetectSubtitleFormatAsync(subtitlePath, cancellationToken);
            var segments = new List<SubtitleSegment>();

            switch (format.ToLower())
            {
                case "srt":
                    segments = await ParseSrtAsync(subtitlePath, cancellationToken);
                    break;
                case "vtt":
                    segments = await ParseVttAsync(subtitlePath, cancellationToken);
                    break;
                case "ass":
                case "ssa":
                    segments = await ParseAssAsync(subtitlePath, cancellationToken);
                    break;
                default:
                    _logger.LogWarning("Unknown subtitle format: {Format}, attempting generic parsing", format);
                    segments = await ParseGenericAsync(subtitlePath, cancellationToken);
                    break;
            }

            _logger.LogInformation("Successfully parsed {Count} subtitle segments from {Format} file", segments.Count, format);
            return segments;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing subtitle file: {SubtitlePath}", subtitlePath);
            throw;
        }
    }

    public async Task<List<SubtitleSegment>> GenerateSubtitlesAsync(string videoPath, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Generating subtitles for video: {VideoPath}", videoPath);
        
        try
        {
            var audioPath = await _whisperService.ExtractAudioFromVideoAsync(videoPath, cancellationToken);
            var segments = await _whisperService.TranscribeAudioAsync(audioPath, cancellationToken);
            
            _logger.LogInformation("Generated {Count} subtitle segments for video", segments.Count);
            return segments;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating subtitles for video: {VideoPath}", videoPath);
            throw;
        }
    }

    public async Task<bool> IsSubtitleFileValidAsync(string subtitlePath, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!File.Exists(subtitlePath))
                return false;

            var format = await DetectSubtitleFormatAsync(subtitlePath, cancellationToken);
            if (string.IsNullOrEmpty(format) || format == "unknown")
                return false;

            // Try to parse a few lines to validate structure
            var lines = await File.ReadAllLinesAsync(subtitlePath, cancellationToken);
            return lines.Length > 0 && !string.IsNullOrWhiteSpace(lines[0]);
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Subtitle file validation failed for: {SubtitlePath}", subtitlePath);
            return false;
        }
    }

    public async Task<string> DetectSubtitleFormatAsync(string subtitlePath, CancellationToken cancellationToken = default)
    {
        try
        {
            var extension = Path.GetExtension(subtitlePath).ToLower();
            
            // Quick detection by file extension
            if (extension == ".srt") return "srt";
            if (extension == ".vtt") return "vtt";
            if (extension == ".ass") return "ass";
            if (extension == ".ssa") return "ssa";

            // Content-based detection for edge cases
            var lines = await File.ReadAllLinesAsync(subtitlePath, cancellationToken);
            if (lines.Length == 0) return "unknown";

            var firstFewLines = string.Join("\n", lines.Take(10));
            
            if (firstFewLines.Contains("WEBVTT")) return "vtt";
            if (firstFewLines.Contains("[Script Info]") || firstFewLines.Contains("Dialogue:")) return "ass";
            if (System.Text.RegularExpressions.Regex.IsMatch(firstFewLines, @"\d+\s*\n\d{2}:\d{2}:\d{2}")) return "srt";

            return "unknown";
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error detecting subtitle format for: {SubtitlePath}", subtitlePath);
            return "unknown";
        }
    }

    private async Task<List<SubtitleSegment>> ParseSrtAsync(string subtitlePath, CancellationToken cancellationToken)
    {
        using var fileStream = File.OpenRead(subtitlePath);
        var parser = new SubtitlesParser.Classes.Parsers.SrtParser();
        var subtitles = parser.ParseStream(fileStream, System.Text.Encoding.UTF8);
        
        return subtitles.Select(sub => new SubtitleSegment
        {
            StartTime = TimeSpan.FromMilliseconds(sub.StartTime),
            EndTime = TimeSpan.FromMilliseconds(sub.EndTime),
            Text = string.Join(" ", sub.Lines),
            Language = "unknown",
            Confidence = 1.0
        }).ToList();
    }

    private async Task<List<SubtitleSegment>> ParseVttAsync(string subtitlePath, CancellationToken cancellationToken)
    {
        using var fileStream = File.OpenRead(subtitlePath);
        var parser = new SubtitlesParser.Classes.Parsers.VttParser();
        var subtitles = parser.ParseStream(fileStream, System.Text.Encoding.UTF8);
        
        return subtitles.Select(sub => new SubtitleSegment
        {
            StartTime = TimeSpan.FromMilliseconds(sub.StartTime),
            EndTime = TimeSpan.FromMilliseconds(sub.EndTime),
            Text = string.Join(" ", sub.Lines),
            Language = "unknown",
            Confidence = 1.0
        }).ToList();
    }

    private async Task<List<SubtitleSegment>> ParseAssAsync(string subtitlePath, CancellationToken cancellationToken)
    {
        using var fileStream = File.OpenRead(subtitlePath);
        var parser = new SubtitlesParser.Classes.Parsers.SsaParser();
        var subtitles = parser.ParseStream(fileStream, System.Text.Encoding.UTF8);
        
        return subtitles.Select(sub => new SubtitleSegment
        {
            StartTime = TimeSpan.FromMilliseconds(sub.StartTime),
            EndTime = TimeSpan.FromMilliseconds(sub.EndTime),
            Text = string.Join(" ", sub.Lines),
            Language = "unknown",
            Confidence = 1.0
        }).ToList();
    }

    private async Task<List<SubtitleSegment>> ParseGenericAsync(string subtitlePath, CancellationToken cancellationToken)
    {
        // Generic parser for unknown formats
        var lines = await File.ReadAllLinesAsync(subtitlePath, cancellationToken);
        var segments = new List<SubtitleSegment>();
        
        for (int i = 0; i < lines.Length; i++)
        {
            var line = lines[i].Trim();
            if (string.IsNullOrEmpty(line)) continue;

            // Try to extract timestamp information using regex
            var timeMatch = System.Text.RegularExpressions.Regex.Match(line, 
                @"(\d{1,2}):(\d{2}):(\d{2})[,.](\d{3})\s*[-–>]+\s*(\d{1,2}):(\d{2}):(\d{2})[,.](\d{3})");
            
            if (timeMatch.Success)
            {
                var startTime = new TimeSpan(0, 
                    int.Parse(timeMatch.Groups[1].Value),
                    int.Parse(timeMatch.Groups[2].Value), 
                    int.Parse(timeMatch.Groups[3].Value),
                    int.Parse(timeMatch.Groups[4].Value));
                
                var endTime = new TimeSpan(0,
                    int.Parse(timeMatch.Groups[5].Value),
                    int.Parse(timeMatch.Groups[6].Value),
                    int.Parse(timeMatch.Groups[7].Value),
                    int.Parse(timeMatch.Groups[8].Value));

                // Look for text in following lines
                var text = "";
                for (int j = i + 1; j < lines.Length && j < i + 5; j++)
                {
                    if (string.IsNullOrWhiteSpace(lines[j]) || 
                        System.Text.RegularExpressions.Regex.IsMatch(lines[j], @"^\d+$"))
                        break;
                    text += lines[j] + " ";
                }

                if (!string.IsNullOrWhiteSpace(text))
                {
                    segments.Add(new SubtitleSegment
                    {
                        StartTime = startTime,
                        EndTime = endTime,
                        Text = text.Trim(),
                        Language = "unknown",
                        Confidence = 0.8 // Lower confidence for generic parsing
                    });
                }
            }
        }

        return segments;
    }
}