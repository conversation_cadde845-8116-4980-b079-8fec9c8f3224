namespace VideoContentAnalyzer.Core.Models;

public class VideoAnalysisResult
{
    public required string VideoPath { get; set; }
    public TimeSpan VideoDuration { get; set; }
    public List<FrameAnalysis> FrameAnalyses { get; set; } = [];
    public List<SubtitleSegment> SubtitleSegments { get; set; } = [];
    public VideoSummary Summary { get; set; } = new();
    public DateTime AnalysisTimestamp { get; set; } = DateTime.UtcNow;
    
    // 新增：效能統計
    public PerformanceMetrics PerformanceMetrics { get; set; } = new();
}

public class VideoSummary
{
    public string OverallDescription { get; set; } = string.Empty;
    public List<string> MainActivities { get; set; } = [];
    public List<string> DetectedObjects { get; set; } = [];
    public List<string> DetectedText { get; set; } = [];
    public List<SceneChange> SceneChanges { get; set; } = [];
    public List<PlaceInfo> PlaceInfos { get; set; } = []; // 新增：識別到的餐廳/景點資訊
    
    // 新增：保存送給 AI 判讀的字幕內容
    public string SubtitleContentForAnalysis { get; set; } = string.Empty;
    public List<string> SubtitleSegmentsForAnalysis { get; set; } = [];
}

public class SceneChange
{
    public TimeSpan Timestamp { get; set; }
    public string Description { get; set; } = string.Empty;
    public double ConfidenceScore { get; set; }
}