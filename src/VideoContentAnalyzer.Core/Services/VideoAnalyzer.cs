using Microsoft.Extensions.Logging;
using VideoContentAnalyzer.Core.Interfaces;
using VideoContentAnalyzer.Core.Models;

namespace VideoContentAnalyzer.Core.Services;

public class VideoAnalyzer : IVideoAnalyzer
{
    private readonly IVideoFrameExtractor _frameExtractor;
    private readonly IAIAnalysisService _aiAnalysisService;
    private readonly ISubtitleService _subtitleService;
    private readonly ILogger<VideoAnalyzer> _logger;

    public VideoAnalyzer(
        IVideoFrameExtractor frameExtractor,
        IAIAnalysisService aiAnalysisService,
        ISubtitleService subtitleService,
        ILogger<VideoAnalyzer> logger)
    {
        _frameExtractor = frameExtractor;
        _aiAnalysisService = aiAnalysisService;
        _subtitleService = subtitleService;
        _logger = logger;
    }

    public async Task<VideoAnalysisResult> AnalyzeVideoAsync(VideoAnalysisRequest request, CancellationToken cancellationToken = default)
    {
        return await AnalyzeVideoAsync(request, null, cancellationToken);
    }

    public async Task<VideoAnalysisResult> AnalyzeVideoAsync(
        VideoAnalysisRequest request, 
        IProgress<AnalysisProgress>? progress = null, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting video analysis for: {VideoPath}", request.VideoPath);
        var startTime = DateTime.UtcNow;
        
        if (!File.Exists(request.VideoPath))
        {
            throw new FileNotFoundException($"Video file not found: {request.VideoPath}");
        }

        var result = new VideoAnalysisResult
        {
            VideoPath = request.VideoPath,
            AnalysisTimestamp = startTime
        };

        try
        {
            // Step 1: Get video duration
            progress?.Report(new AnalysisProgress 
            { 
                CurrentStage = "Analyzing video metadata", 
                ProgressPercentage = 0,
                Message = "Getting video duration...",
                Elapsed = DateTime.UtcNow - startTime
            });

            result.VideoDuration = await _frameExtractor.GetVideoDurationAsync(request.VideoPath, cancellationToken);
            _logger.LogInformation("Video duration: {Duration}", result.VideoDuration);

            // Step 2: Extract frames
            progress?.Report(new AnalysisProgress 
            { 
                CurrentStage = "Extracting video frames", 
                ProgressPercentage = 10,
                Message = "Extracting key frames from video...",
                Elapsed = DateTime.UtcNow - startTime
            });

            var extractedFrames = await _frameExtractor.ExtractFramesAsync(request.VideoPath, request.Options, cancellationToken);
            _logger.LogInformation("Extracted {Count} frames", extractedFrames.Count);

            // Step 3: Process subtitles
            progress?.Report(new AnalysisProgress 
            { 
                CurrentStage = "Processing subtitles", 
                ProgressPercentage = 25,
                Message = "Loading or generating subtitles...",
                Elapsed = DateTime.UtcNow - startTime
            });

            result.SubtitleSegments = await ProcessSubtitlesAsync(request, cancellationToken);
            _logger.LogInformation("Processed {Count} subtitle segments", result.SubtitleSegments.Count);

            // Step 4: Analyze frames with AI
            progress?.Report(new AnalysisProgress 
            { 
                CurrentStage = "AI Frame Analysis", 
                ProgressPercentage = 40,
                Message = "Analyzing video frames with AI...",
                Elapsed = DateTime.UtcNow - startTime
            });

            result.FrameAnalyses = await AnalyzeFramesAsync(extractedFrames, request.Options, progress, startTime, cancellationToken);
            _logger.LogInformation("Completed AI analysis for {Count} frames", result.FrameAnalyses.Count);

            // Step 5: Analyze subtitle content
            if (result.SubtitleSegments.Any())
            {
                progress?.Report(new AnalysisProgress 
                { 
                    CurrentStage = "Subtitle Analysis", 
                    ProgressPercentage = 80,
                    Message = "Analyzing subtitle content...",
                    Elapsed = DateTime.UtcNow - startTime
                });

                await AnalyzeSubtitleContentAsync(result.SubtitleSegments, cancellationToken);
            }

            // Step 6: Generate summary
            progress?.Report(new AnalysisProgress 
            { 
                CurrentStage = "Generating Summary", 
                ProgressPercentage = 90,
                Message = "Generating video summary...",
                Elapsed = DateTime.UtcNow - startTime
            });

            result.Summary = await GenerateSummaryAsync(result.FrameAnalyses, result.SubtitleSegments, cancellationToken);

            // Step 6.5: Calculate performance metrics
            result.PerformanceMetrics = CalculatePerformanceMetrics(result.FrameAnalyses, startTime, DateTime.UtcNow);

            // Step 7: Complete
            progress?.Report(new AnalysisProgress 
            { 
                CurrentStage = "Complete", 
                ProgressPercentage = 100,
                Message = "Analysis completed successfully",
                Elapsed = DateTime.UtcNow - startTime
            });

            _logger.LogInformation("Video analysis completed successfully in {Elapsed}", DateTime.UtcNow - startTime);
            _logger.LogInformation("Performance metrics - Avg frame analysis: {AvgTime}ms, Total frames: {Total}, Success rate: {SuccessRate}%",
                result.PerformanceMetrics.AverageFrameAnalysisTime.TotalMilliseconds,
                result.PerformanceMetrics.TotalFramesAnalyzed,
                result.PerformanceMetrics.TotalFramesAnalyzed > 0 ? (result.PerformanceMetrics.SuccessfulAnalyses * 100.0 / result.PerformanceMetrics.TotalFramesAnalyzed) : 0);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during video analysis: {VideoPath}", request.VideoPath);
            progress?.Report(new AnalysisProgress 
            { 
                CurrentStage = "Error", 
                ProgressPercentage = 0,
                Message = $"Analysis failed: {ex.Message}",
                Elapsed = DateTime.UtcNow - startTime
            });
            throw;
        }
    }

    private async Task<List<SubtitleSegment>> ProcessSubtitlesAsync(VideoAnalysisRequest request, CancellationToken cancellationToken)
    {
        if (!string.IsNullOrEmpty(request.SubtitlePath) && File.Exists(request.SubtitlePath))
        {
            _logger.LogInformation("Loading subtitles from file: {SubtitlePath}", request.SubtitlePath);
            
            if (await _subtitleService.IsSubtitleFileValidAsync(request.SubtitlePath, cancellationToken))
            {
                return await _subtitleService.ParseSubtitleFileAsync(request.SubtitlePath, cancellationToken);
            }
            else
            {
                _logger.LogWarning("Invalid subtitle file, will generate subtitles instead");
            }
        }

        if (request.Options.GenerateSubtitlesIfMissing)
        {
            _logger.LogInformation("Generating subtitles using Whisper");
            return await _subtitleService.GenerateSubtitlesAsync(request.VideoPath, cancellationToken);
        }

        return [];
    }

    private async Task<List<FrameAnalysis>> AnalyzeFramesAsync(
        List<ExtractedFrame> extractedFrames, 
        VideoAnalysisOptions options,
        IProgress<AnalysisProgress>? progress,
        DateTime startTime,
        CancellationToken cancellationToken)
    {
        var frameAnalyses = new List<FrameAnalysis>();
        var totalFrames = extractedFrames.Count;

        // Process frames in batches to avoid overwhelming the AI service
        const int batchSize = 10;
        var batches = extractedFrames
            .Select((frame, index) => new { Frame = frame, Index = index })
            .GroupBy(x => x.Index / batchSize)
            .Select(g => g.Select(x => x.Frame).ToList())
            .ToList();

        for (int batchIndex = 0; batchIndex < batches.Count; batchIndex++)
        {
            var batch = batches[batchIndex];
            var batchProgress = 40 + (int)((double)(batchIndex * batchSize) / totalFrames * 35);
            
            progress?.Report(new AnalysisProgress 
            { 
                CurrentStage = "AI Frame Analysis", 
                ProgressPercentage = batchProgress,
                Message = $"Analyzing frames {batchIndex * batchSize + 1}-{Math.Min((batchIndex + 1) * batchSize, totalFrames)} of {totalFrames}",
                Elapsed = DateTime.UtcNow - startTime
            });

            var batchTasks = batch.Select(async frame =>
            {
                try
                {
                    var frameAnalysis = new FrameAnalysis
                    {
                        Timestamp = frame.Timestamp,
                        FramePath = frame.FramePath,
                        AnalysisStartTime = DateTime.UtcNow
                    };

                    // Analyze scene with timing
                    var (sceneResult, sceneDuration) = await _aiAnalysisService.AnalyzeFrameWithTimingAsync(frame.FramePath, cancellationToken);
                    frameAnalysis.Scene = sceneResult;
                    frameAnalysis.ConfidenceScore = 0.8; // Default confidence

                    // Extract text if enabled
                    TimeSpan textDuration = TimeSpan.Zero;
                    if (options.EnableTextRecognition)
                    {
                        var textStartTime = DateTime.UtcNow;
                        frameAnalysis.DetectedTexts = await _aiAnalysisService.ExtractTextFromFrameAsync(frame.FramePath, cancellationToken);
                        textDuration = DateTime.UtcNow - textStartTime;
                    }

                    // Extract place information to populate PlaceInfos
                    TimeSpan placeInfoDuration = TimeSpan.Zero;
                    if (options.EnablePlaceRecognition)
                    {
                        var placeInfoStartTime = DateTime.UtcNow;
                        frameAnalysis.PlaceInfos = await _aiAnalysisService.ExtractPlaceInfoAsync(frame.FramePath, cancellationToken);
                        placeInfoDuration = DateTime.UtcNow - placeInfoStartTime;
                    }
                    else
                    {
                        // Even without place recognition enabled, try to extract basic restaurant info from scene analysis
                        frameAnalysis.PlaceInfos = ExtractBasicPlaceInfoFromScene(frameAnalysis.Scene);
                    }

                    // Set timing information
                    frameAnalysis.AnalysisEndTime = DateTime.UtcNow;
                    frameAnalysis.AnalysisDuration = sceneDuration + textDuration + placeInfoDuration;

                    return frameAnalysis;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to analyze frame at {Timestamp}", frame.Timestamp);
                    return new FrameAnalysis
                    {
                        Timestamp = frame.Timestamp,
                        FramePath = frame.FramePath,
                        Scene = new SceneDescription { MainDescription = "Analysis failed" },
                        ConfidenceScore = 0.0,
                        AnalysisStartTime = DateTime.UtcNow,
                        AnalysisEndTime = DateTime.UtcNow,
                        AnalysisDuration = TimeSpan.Zero,
                        PlaceInfos = []
                    };
                }
            });

            var batchResults = await Task.WhenAll(batchTasks);
            frameAnalyses.AddRange(batchResults);

            // Longer delay between batches for vision model processing
            await Task.Delay(2000, cancellationToken);
        }

        return frameAnalyses;
    }

    /// <summary>
    /// 從場景分析結果中提取基本的餐廳資訊
    /// </summary>
    private List<PlaceInfo> ExtractBasicPlaceInfoFromScene(SceneDescription scene)
    {
        var placeInfos = new List<PlaceInfo>();
        
        // 檢查是否為餐廳場景
        if (string.IsNullOrEmpty(scene.RestaurantCategory) || scene.RestaurantCategory == "非餐廳")
            return placeInfos;

        // 從 visibleTexts 中尋找可能的餐廳名稱
        var potentialRestaurantNames = scene.VisibleTexts
            .Where(text => !string.IsNullOrWhiteSpace(text) && 
                          text.Length > 1 && 
                          text.Length < 50 && // 避免過長的文字
                          IsLikelyRestaurantName(text))
            .ToList();

        foreach (var name in potentialRestaurantNames)
        {
            placeInfos.Add(new PlaceInfo
            {
                Name = name,
                Category = scene.RestaurantCategory != "非餐廳" ? scene.RestaurantCategory : scene.CuisineType,
                Description = scene.MainDescription,
                Confidence = 0.7, // 中等信心度，因為是從場景分析推斷的
                OriginalTexts = [name],
                Address = ExtractAddressFromSetting(scene.Setting),
                BusinessHours = null,
                Phone = null,
                Website = null
            });
        }

        return placeInfos;
    }

    /// <summary>
    /// 判斷文字是否可能是餐廳名稱
    /// </summary>
    private static bool IsLikelyRestaurantName(string text)
    {
        // 排除純數字、單字元、常見非名稱文字
        if (text.All(char.IsDigit) || 
            text.Length <= 1 ||
            text.All(c => char.IsWhiteSpace(c) || char.IsPunctuation(c)))
            return false;

        // 排除明顯的非名稱文字
        var excludePatterns = new[] { "第", "位", "名", "営業中", "閉店", "open", "close", "menu", "price" };
        if (excludePatterns.Any(pattern => text.Contains(pattern, StringComparison.OrdinalIgnoreCase)))
            return false;

        // 包含餐廳相關關鍵字的更可能是店名
        var restaurantKeywords = new[] { "麺", "麵", "食堂", "店", "屋", "亭", "館", "家", "房", "kitchen", "cafe", "restaurant", "grill", "bar" };
        if (restaurantKeywords.Any(keyword => text.Contains(keyword, StringComparison.OrdinalIgnoreCase)))
            return true;

        // 其他可能包含店名的模式（如日文、中文店名）
        return text.Any(c => char.GetUnicodeCategory(c) == System.Globalization.UnicodeCategory.OtherLetter);
    }

    /// <summary>
    /// 從設定描述中提取地址資訊
    /// </summary>
    private static string? ExtractAddressFromSetting(string setting)
    {
        if (string.IsNullOrWhiteSpace(setting))
            return null;

        // 尋找包含地址關鍵字的句子
        var addressKeywords = new[] { "位於", "地址", "地點", "附近", "區", "市", "街", "路", "道", "丁目", "番地" };
        if (addressKeywords.Any(keyword => setting.Contains(keyword)))
        {
            // 如果設定中包含地址相關資訊，返回簡化的地址描述
            return setting;
        }

        return null;
    }

    private async Task AnalyzeSubtitleContentAsync(List<SubtitleSegment> subtitleSegments, CancellationToken cancellationToken)
    {
        var analysisTasks = subtitleSegments
            .Where(s => !string.IsNullOrWhiteSpace(s.Text))
            .Take(50) // Limit to avoid token limits
            .Select(async segment =>
            {
                try
                {
                    segment.Analysis = await _aiAnalysisService.AnalyzeSubtitleSegmentAsync(segment.Text, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to analyze subtitle segment: {Text}", segment.Text);
                    segment.Analysis = new SubtitleAnalysis();
                }
            });

        await Task.WhenAll(analysisTasks);
    }

    private async Task<VideoSummary> GenerateSummaryAsync(
        List<FrameAnalysis> frameAnalyses, 
        List<SubtitleSegment> subtitleSegments, 
        CancellationToken cancellationToken)
    {
        try
        {
            var summary = new VideoSummary();

            // Generate overall description with subtitle content
            var (summaryText, subtitleContent, subtitleSegmentsForAnalysis) = await _aiAnalysisService.GenerateVideoSummaryAsync(frameAnalyses, subtitleSegments, cancellationToken);
            summary.OverallDescription = summaryText;
            summary.SubtitleContentForAnalysis = subtitleContent;
            summary.SubtitleSegmentsForAnalysis = subtitleSegmentsForAnalysis;

            // Aggregate activities from frame analyses
            summary.MainActivities = frameAnalyses
                .SelectMany(f => f.Scene.Activities)
                .GroupBy(a => a.ToLower())
                .OrderByDescending(g => g.Count())
                .Take(10)
                .Select(g => g.Key)
                .ToList();

            // Aggregate detected objects
            summary.DetectedObjects = frameAnalyses
                .SelectMany(f => f.Scene.Objects)
                .GroupBy(o => o.Name.ToLower())
                .OrderByDescending(g => g.Count())
                .Take(15)
                .Select(g => g.Key)
                .ToList();

            // Aggregate detected text
            summary.DetectedText = frameAnalyses
                .SelectMany(f => f.DetectedTexts)
                .Where(t => !string.IsNullOrWhiteSpace(t.Text))
                .GroupBy(t => t.Text.ToLower())
                .OrderByDescending(g => g.Count())
                .Take(20)
                .Select(g => g.Key)
                .ToList();

            // Aggregate place information from frame analyses
            summary.PlaceInfos = frameAnalyses
                .SelectMany(f => f.PlaceInfos)
                .Where(p => p.Confidence > 0.3) // 過濾低信心度的結果
                .GroupBy(p => p.Name?.ToLower())
                .Where(g => !string.IsNullOrWhiteSpace(g.Key)) // 確保名稱不為空
                .Select(g => g.OrderByDescending(p => p.Confidence).First()) // 取信心度最高的
                .OrderByDescending(p => p.Confidence)
                .Take(10)
                .ToList();

            // Identify scene changes (simplified)
            summary.SceneChanges = frameAnalyses
                .Where((f, i) => i > 0 && 
                    !string.Equals(f.Scene.Setting, frameAnalyses[i-1].Scene.Setting, StringComparison.OrdinalIgnoreCase))
                .Select(f => new SceneChange
                {
                    Timestamp = f.Timestamp,
                    Description = $"Scene change to: {f.Scene.Setting}",
                    ConfidenceScore = f.ConfidenceScore
                })
                .ToList();

            return summary;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating video summary");
            return new VideoSummary
            {
                OverallDescription = "Unable to generate summary due to analysis error."
            };
        }
    }

    private PerformanceMetrics CalculatePerformanceMetrics(List<FrameAnalysis> frameAnalyses, DateTime analysisStartTime, DateTime analysisEndTime)
    {
        var metrics = new PerformanceMetrics
        {
            TotalAnalysisTime = analysisEndTime - analysisStartTime,
            TotalFramesAnalyzed = frameAnalyses.Count
        };

        var validAnalyses = frameAnalyses.Where(f => f.AnalysisDuration > TimeSpan.Zero).ToList();
        
        if (validAnalyses.Any())
        {
            var durations = validAnalyses.Select(f => f.AnalysisDuration).ToList();
            
            metrics.AverageFrameAnalysisTime = TimeSpan.FromMilliseconds(durations.Average(d => d.TotalMilliseconds));
            metrics.FastestFrameAnalysisTime = durations.Min();
            metrics.SlowestFrameAnalysisTime = durations.Max();
            metrics.SuccessfulAnalyses = validAnalyses.Count;
            metrics.FailedAnalyses = frameAnalyses.Count - validAnalyses.Count;

            // Create detailed timing info
            metrics.DetailedTimings = validAnalyses.Select(f => new AnalysisTimingInfo
            {
                Duration = f.AnalysisDuration,
                StartTime = f.AnalysisStartTime,
                EndTime = f.AnalysisEndTime,
                Stage = "FrameAnalysis"
            }).ToList();
        }
        else
        {
            metrics.FailedAnalyses = frameAnalyses.Count;
        }

        return metrics;
    }
}